<div class="header bg-gradient-danger pb-8 pt-5 pt-md-8">
  <div class="container-fluid">
    <div class="header-body">
      <!-- Card stats -->
      <div class="row">
        <div class="col-xl-3 col-lg-6">
          <div class="card card-stats mb-4 mb-xl-0">
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5 class="card-title text-uppercase text-muted mb-0">Traffic</h5>
                  <span class="h2 font-weight-bold mb-0">350,897</span>
                </div>
                <div class="col-auto">
                  <div class="icon icon-shape bg-danger text-white rounded-circle shadow">
                    <i class="fas fa-chart-bar"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 mb-0 text-muted text-sm">
                <span class="text-success mr-2"><i class="fa fa-arrow-up"></i> 3.48%</span>
                <span class="text-nowrap">Since last month</span>
              </p>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-6">
          <div class="card card-stats mb-4 mb-xl-0">
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5 class="card-title text-uppercase text-muted mb-0">New users</h5>
                  <span class="h2 font-weight-bold mb-0">2,356</span>
                </div>
                <div class="col-auto">
                  <div class="icon icon-shape bg-warning text-white rounded-circle shadow">
                    <i class="fas fa-chart-pie"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 mb-0 text-muted text-sm">
                <span class="text-danger mr-2"><i class="fas fa-arrow-down"></i> 3.48%</span>
                <span class="text-nowrap">Since last week</span>
              </p>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-6">
          <div class="card card-stats mb-4 mb-xl-0">
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5 class="card-title text-uppercase text-muted mb-0">Sales</h5>
                  <span class="h2 font-weight-bold mb-0">924</span>
                </div>
                <div class="col-auto">
                  <div class="icon icon-shape bg-yellow text-white rounded-circle shadow">
                    <i class="fas fa-users"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 mb-0 text-muted text-sm">
                <span class="text-warning mr-2"><i class="fas fa-arrow-down"></i> 1.10%</span>
                <span class="text-nowrap">Since yesterday</span>
              </p>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-lg-6">
          <div class="card card-stats mb-4 mb-xl-0">
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5 class="card-title text-uppercase text-muted mb-0">Performance</h5>
                  <span class="h2 font-weight-bold mb-0">49,65%</span>
                </div>
                <div class="col-auto">
                  <div class="icon icon-shape bg-info text-white rounded-circle shadow">
                    <i class="fas fa-percent"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 mb-0 text-muted text-sm">
                <span class="text-success mr-2"><i class="fas fa-arrow-up"></i> 12%</span>
                <span class="text-nowrap">Since last month</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Page content -->
<div class="container-fluid mt--7">
  <!-- Table -->
  <div class="row">
    <div class="col">
      <div class="card shadow">
        <div class="card-header bg-transparent">
          <h3 class="mb-0">Icons</h3>
        </div>
        <div class="card-body">
          <div class="row icon-examples">
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'active-40' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'active-40'" (cbOnSuccess) = "copy = 'active-40'">
                <div>
                  <i class="ni ni-active-40"></i>
                  <span>active-40</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'air-baloon' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'air-baloon'" (cbOnSuccess) = "copy = 'air-baloon'">
                <div>
                  <i class="ni ni-air-baloon"></i>
                  <span>air-baloon</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'album-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'album-2'" (cbOnSuccess) = "copy = 'album-2'">
                <div>
                  <i class="ni ni-album-2"></i>
                  <span>album-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'align-center' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'align-center'" (cbOnSuccess) = "copy = 'align-center'">
                <div>
                  <i class="ni ni-align-center"></i>
                  <span>align-center</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'align-left-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'align-left-2'" (cbOnSuccess) = "copy = 'align-left-2'">
                <div>
                  <i class="ni ni-align-left-2"></i>
                  <span>align-left-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'ambulance' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'ambulance'" (cbOnSuccess) = "copy = 'ambulance'">
                <div>
                  <i class="ni ni-ambulance"></i>
                  <span>ambulance</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'app' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'app'" (cbOnSuccess) = "copy = 'app'">
                <div>
                  <i class="ni ni-app"></i>
                  <span>app</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'archive-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'archive-2'" (cbOnSuccess) = "copy = 'archive-2'">
                <div>
                  <i class="ni ni-archive-2"></i>
                  <span>archive-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'atom' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'atom'" (cbOnSuccess) = "copy = 'atom'">
                <div>
                  <i class="ni ni-atom"></i>
                  <span>atom</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'badge' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'badge'" (cbOnSuccess) = "copy = 'badge'">
                <div>
                  <i class="ni ni-badge"></i>
                  <span>badge</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bag-17' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bag-17'" (cbOnSuccess) = "copy = 'bag-17'">
                <div>
                  <i class="ni ni-bag-17"></i>
                  <span>bag-17</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'basket' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'basket'" (cbOnSuccess) = "copy = 'basket'">
                <div>
                  <i class="ni ni-basket"></i>
                  <span>basket</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bell-55' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bell-55'" (cbOnSuccess) = "copy = 'bell-55'">
                <div>
                  <i class="ni ni-bell-55"></i>
                  <span>bell-55</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bold-down' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bold-down'" (cbOnSuccess) = "copy = 'bold-down'">
                <div>
                  <i class="ni ni-bold-down"></i>
                  <span>bold-down</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bold-left' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bold-left'" (cbOnSuccess) = "copy = 'bold-left'">
                <div>
                  <i class="ni ni-bold-left"></i>
                  <span>bold-left</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bold-right' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bold-right'" (cbOnSuccess) = "copy = 'bold-right'">
                <div>
                  <i class="ni ni-bold-right"></i>
                  <span>bold-right</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bold-up' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bold-up'" (cbOnSuccess) = "copy = 'bold-up'">
                <div>
                  <i class="ni ni-bold-up"></i>
                  <span>bold-up</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bold' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bold'" (cbOnSuccess) = "copy = 'bold'">
                <div>
                  <i class="ni ni-bold"></i>
                  <span>bold</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'book-bookmark' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'book-bookmark'" (cbOnSuccess) = "copy = 'book-bookmark'">
                <div>
                  <i class="ni ni-book-bookmark"></i>
                  <span>book-bookmark</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'books' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'books'" (cbOnSuccess) = "copy = 'books'">
                <div>
                  <i class="ni ni-books"></i>
                  <span>books</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'box-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'box-2'" (cbOnSuccess) = "copy = 'box-2'">
                <div>
                  <i class="ni ni-box-2"></i>
                  <span>box-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'briefcase-24' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'briefcase-24'" (cbOnSuccess) = "copy = 'briefcase-24'">
                <div>
                  <i class="ni ni-briefcase-24"></i>
                  <span>briefcase-24</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'building' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'building'" (cbOnSuccess) = "copy = 'building'">
                <div>
                  <i class="ni ni-building"></i>
                  <span>building</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bulb-61' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bulb-61'" (cbOnSuccess) = "copy = 'bulb-61'">
                <div>
                  <i class="ni ni-bulb-61"></i>
                  <span>bulb-61</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bullet-list-67' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bullet-list-67'" (cbOnSuccess) = "copy = 'bullet-list-67'">
                <div>
                  <i class="ni ni-bullet-list-67"></i>
                  <span>bullet-list-67</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'bus-front-12' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'bus-front-12'" (cbOnSuccess) = "copy = 'bus-front-12'">
                <div>
                  <i class="ni ni-bus-front-12"></i>
                  <span>bus-front-12</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'button-pause' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'button-pause'" (cbOnSuccess) = "copy = 'button-pause'">
                <div>
                  <i class="ni ni-button-pause"></i>
                  <span>button-pause</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'button-play' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'button-play'" (cbOnSuccess) = "copy = 'button-play'">
                <div>
                  <i class="ni ni-button-play"></i>
                  <span>button-play</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'button-power' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'button-power'" (cbOnSuccess) = "copy = 'button-power'">
                <div>
                  <i class="ni ni-button-power"></i>
                  <span>button-power</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'calendar-grid-58' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'calendar-grid-58'" (cbOnSuccess) = "copy = 'calendar-grid-58'">
                <div>
                  <i class="ni ni-calendar-grid-58"></i>
                  <span>calendar-grid-58</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'camera-compact' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'camera-compact'" (cbOnSuccess) = "copy = 'camera-compact'">
                <div>
                  <i class="ni ni-camera-compact"></i>
                  <span>camera-compact</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'caps-small' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'caps-small'" (cbOnSuccess) = "copy = 'caps-small'">
                <div>
                  <i class="ni ni-caps-small"></i>
                  <span>caps-small</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'cart' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'cart'" (cbOnSuccess) = "copy = 'cart'">
                <div>
                  <i class="ni ni-cart"></i>
                  <span>cart</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'chart-bar-32' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'chart-bar-32'" (cbOnSuccess) = "copy = 'chart-bar-32'">
                <div>
                  <i class="ni ni-chart-bar-32"></i>
                  <span>chart-bar-32</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'chart-pie-35' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'chart-pie-35'" (cbOnSuccess) = "copy = 'chart-pie-35'">
                <div>
                  <i class="ni ni-chart-pie-35"></i>
                  <span>chart-pie-35</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'chat-round' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'chat-round'" (cbOnSuccess) = "copy = 'chat-round'">
                <div>
                  <i class="ni ni-chat-round"></i>
                  <span>chat-round</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'check-bold' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'check-bold'" (cbOnSuccess) = "copy = 'check-bold'">
                <div>
                  <i class="ni ni-check-bold"></i>
                  <span>check-bold</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'circle-08' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'circle-08'" (cbOnSuccess) = "copy = 'circle-08'">
                <div>
                  <i class="ni ni-circle-08"></i>
                  <span>circle-08</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'cloud-download-95' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'cloud-download-95'" (cbOnSuccess) = "copy = 'cloud-download-95'">
                <div>
                  <i class="ni ni-cloud-download-95"></i>
                  <span>cloud-download-95</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'cloud-upload-96' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'cloud-upload-96'" (cbOnSuccess) = "copy = 'cloud-upload-96'">
                <div>
                  <i class="ni ni-cloud-upload-96"></i>
                  <span>cloud-upload-96</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'compass-04' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'compass-04'" (cbOnSuccess) = "copy = 'compass-04'">
                <div>
                  <i class="ni ni-compass-04"></i>
                  <span>compass-04</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'controller' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'controller'" (cbOnSuccess) = "copy = 'controller'">
                <div>
                  <i class="ni ni-controller"></i>
                  <span>controller</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'credit-card' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'credit-card'" (cbOnSuccess) = "copy = 'credit-card'">
                <div>
                  <i class="ni ni-credit-card"></i>
                  <span>credit-card</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'curved-next' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'curved-next'" (cbOnSuccess) = "copy = 'curved-next'">
                <div>
                  <i class="ni ni-curved-next"></i>
                  <span>curved-next</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'delivery-fast' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'delivery-fast'" (cbOnSuccess) = "copy = 'delivery-fast'">
                <div>
                  <i class="ni ni-delivery-fast"></i>
                  <span>delivery-fast</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'diamond' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'diamond'" (cbOnSuccess) = "copy = 'diamond'">
                <div>
                  <i class="ni ni-diamond"></i>
                  <span>diamond</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'email-83' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'email-83'" (cbOnSuccess) = "copy = 'email-83'">
                <div>
                  <i class="ni ni-email-83"></i>
                  <span>email-83</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'fat-add' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'fat-add'" (cbOnSuccess) = "copy = 'fat-add'">
                <div>
                  <i class="ni ni-fat-add"></i>
                  <span>fat-add</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'fat-delete' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'fat-delete'" (cbOnSuccess) = "copy = 'fat-delete'">
                <div>
                  <i class="ni ni-fat-delete"></i>
                  <span>fat-delete</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'fat-remove' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'fat-remove'" (cbOnSuccess) = "copy = 'fat-remove'">
                <div>
                  <i class="ni ni-fat-remove"></i>
                  <span>fat-remove</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'favourite-28' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'favourite-28'" (cbOnSuccess) = "copy = 'favourite-28'">
                <div>
                  <i class="ni ni-favourite-28"></i>
                  <span>favourite-28</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'folder-17' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'folder-17'" (cbOnSuccess) = "copy = 'folder-17'">
                <div>
                  <i class="ni ni-folder-17"></i>
                  <span>folder-17</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'glasses-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'glasses-2'" (cbOnSuccess) = "copy = 'glasses-2'">
                <div>
                  <i class="ni ni-glasses-2"></i>
                  <span>glasses-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'hat-3' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'hat-3'" (cbOnSuccess) = "copy = 'hat-3'">
                <div>
                  <i class="ni ni-hat-3"></i>
                  <span>hat-3</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'headphones' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'headphones'" (cbOnSuccess) = "copy = 'headphones'">
                <div>
                  <i class="ni ni-headphones"></i>
                  <span>headphones</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'html5' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'html5'" (cbOnSuccess) = "copy = 'html5'">
                <div>
                  <i class="ni ni-html5"></i>
                  <span>html5</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'istanbul' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'istanbul'" (cbOnSuccess) = "copy = 'istanbul'">
                <div>
                  <i class="ni ni-istanbul"></i>
                  <span>istanbul</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'circle-08' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'circle-08'" (cbOnSuccess) = "copy = 'circle-08'">
                <div>
                  <i class="ni ni-circle-08"></i>
                  <span>circle-08</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'key-25' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'key-25'" (cbOnSuccess) = "copy = 'key-25'">
                <div>
                  <i class="ni ni-key-25"></i>
                  <span>key-25</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'laptop' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'laptop'" (cbOnSuccess) = "copy = 'laptop'">
                <div>
                  <i class="ni ni-laptop"></i>
                  <span>laptop</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'like-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'like-2'" (cbOnSuccess) = "copy = 'like-2'">
                <div>
                  <i class="ni ni-like-2"></i>
                  <span>like-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'lock-circle-open' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'lock-circle-open'" (cbOnSuccess) = "copy = 'lock-circle-open'">
                <div>
                  <i class="ni ni-lock-circle-open"></i>
                  <span>lock-circle-open</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'map-big' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'map-big'" (cbOnSuccess) = "copy = 'map-big'">
                <div>
                  <i class="ni ni-map-big"></i>
                  <span>map-big</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'mobile-button' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'mobile-button'" (cbOnSuccess) = "copy = 'mobile-button'">
                <div>
                  <i class="ni ni-mobile-button"></i>
                  <span>mobile-button</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'money-coins' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'money-coins'" (cbOnSuccess) = "copy = 'money-coins'">
                <div>
                  <i class="ni ni-money-coins"></i>
                  <span>money-coins</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'note-03' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'note-03'" (cbOnSuccess) = "copy = 'note-03'">
                <div>
                  <i class="ni ni-note-03"></i>
                  <span>note-03</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'notification-70' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'notification-70'" (cbOnSuccess) = "copy = 'notification-70'">
                <div>
                  <i class="ni ni-notification-70"></i>
                  <span>notification-70</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'palette' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'palette'" (cbOnSuccess) = "copy = 'palette'">
                <div>
                  <i class="ni ni-palette"></i>
                  <span>palette</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'paper-diploma' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'paper-diploma'" (cbOnSuccess) = "copy = 'paper-diploma'">
                <div>
                  <i class="ni ni-paper-diploma"></i>
                  <span>paper-diploma</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'pin-3' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'pin-3'" (cbOnSuccess) = "copy = 'pin-3'">
                <div>
                  <i class="ni ni-pin-3"></i>
                  <span>pin-3</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'planet' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'planet'" (cbOnSuccess) = "copy = 'planet'">
                <div>
                  <i class="ni ni-planet"></i>
                  <span>planet</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'ruler-pencil' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'ruler-pencil'" (cbOnSuccess) = "copy = 'ruler-pencil'">
                <div>
                  <i class="ni ni-ruler-pencil"></i>
                  <span>ruler-pencil</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'satisfied' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'satisfied'" (cbOnSuccess) = "copy = 'satisfied'">
                <div>
                  <i class="ni ni-satisfied"></i>
                  <span>satisfied</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'scissors' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'scissors'" (cbOnSuccess) = "copy = 'scissors'">
                <div>
                  <i class="ni ni-scissors"></i>
                  <span>scissors</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'send' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'send'" (cbOnSuccess) = "copy = 'send'">
                <div>
                  <i class="ni ni-send"></i>
                  <span>send</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'settings-gear-65' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'settings-gear-65'" (cbOnSuccess) = "copy = 'settings-gear-65'">
                <div>
                  <i class="ni ni-settings-gear-65"></i>
                  <span>settings-gear-65</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'settings' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'settings'" (cbOnSuccess) = "copy = 'settings'">
                <div>
                  <i class="ni ni-settings"></i>
                  <span>settings</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'single-02' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'single-02'" (cbOnSuccess) = "copy = 'single-02'">
                <div>
                  <i class="ni ni-single-02"></i>
                  <span>single-02</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'single-copy-04' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'single-copy-04'" (cbOnSuccess) = "copy = 'single-copy-04'">
                <div>
                  <i class="ni ni-single-copy-04"></i>
                  <span>single-copy-04</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'sound-wave' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'sound-wave'" (cbOnSuccess) = "copy = 'sound-wave'">
                <div>
                  <i class="ni ni-sound-wave"></i>
                  <span>sound-wave</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'spaceship' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'spaceship'" (cbOnSuccess) = "copy = 'spaceship'">
                <div>
                  <i class="ni ni-spaceship"></i>
                  <span>spaceship</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'square-pin' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'square-pin'" (cbOnSuccess) = "copy = 'square-pin'">
                <div>
                  <i class="ni ni-square-pin"></i>
                  <span>square-pin</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'support-16' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'support-16'" (cbOnSuccess) = "copy = 'support-16'">
                <div>
                  <i class="ni ni-support-16"></i>
                  <span>support-16</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'tablet-button' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'tablet-button'" (cbOnSuccess) = "copy = 'tablet-button'">
                <div>
                  <i class="ni ni-tablet-button"></i>
                  <span>tablet-button</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'tag' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'tag'" (cbOnSuccess) = "copy = 'tag'">
                <div>
                  <i class="ni ni-tag"></i>
                  <span>tag</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'tie-bow' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'tie-bow'" (cbOnSuccess) = "copy = 'tie-bow'">
                <div>
                  <i class="ni ni-tie-bow"></i>
                  <span>tie-bow</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'time-alarm' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'time-alarm'" (cbOnSuccess) = "copy = 'time-alarm'">
                <div>
                  <i class="ni ni-time-alarm"></i>
                  <span>time-alarm</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'trophy' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'trophy'" (cbOnSuccess) = "copy = 'trophy'">
                <div>
                  <i class="ni ni-trophy"></i>
                  <span>trophy</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'tv-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'tv-2'" (cbOnSuccess) = "copy = 'tv-2'">
                <div>
                  <i class="ni ni-tv-2"></i>
                  <span>tv-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'umbrella-13' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'umbrella-13'" (cbOnSuccess) = "copy = 'umbrella-13'">
                <div>
                  <i class="ni ni-umbrella-13"></i>
                  <span>umbrella-13</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'user-run' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'user-run'" (cbOnSuccess) = "copy = 'user-run'">
                <div>
                  <i class="ni ni-user-run"></i>
                  <span>user-run</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'vector' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'vector'" (cbOnSuccess) = "copy = 'vector'">
                <div>
                  <i class="ni ni-vector"></i>
                  <span>vector</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'watch-time' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'watch-time'" (cbOnSuccess) = "copy = 'watch-time'">
                <div>
                  <i class="ni ni-watch-time"></i>
                  <span>watch-time</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'world' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'world'" (cbOnSuccess) = "copy = 'world'">
                <div>
                  <i class="ni ni-world"></i>
                  <span>world</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'zoom-split-in' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'zoom-split-in'" (cbOnSuccess) = "copy = 'zoom-split-in'">
                <div>
                  <i class="ni ni-zoom-split-in"></i>
                  <span>zoom-split-in</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'collection' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'collection'" (cbOnSuccess) = "copy = 'collection'">
                <div>
                  <i class="ni ni-collection"></i>
                  <span>collection</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'image' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'image'" (cbOnSuccess) = "copy = 'image'">
                <div>
                  <i class="ni ni-image"></i>
                  <span>image</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'shop' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'shop'" (cbOnSuccess) = "copy = 'shop'">
                <div>
                  <i class="ni ni-shop"></i>
                  <span>shop</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'ungroup' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'ungroup'" (cbOnSuccess) = "copy = 'ungroup'">
                <div>
                  <i class="ni ni-ungroup"></i>
                  <span>ungroup</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'world-2' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'world-2'" (cbOnSuccess) = "copy = 'world-2'">
                <div>
                  <i class="ni ni-world-2"></i>
                  <span>world-2</span>
                </div>
              </button>
            </div>
            <div class="col-lg-3 col-md-6">
              <button type="button" placement="top-center" triggers="hover focus click" ngbTooltip="{{copy === 'ui-04' ? 'Copied':'Copy to clipboard'}}"  class="btn-icon-clipboard" ngxClipboard [cbContent]="'ui-04'" (cbOnSuccess) = "copy = 'ui-04'">
                <div>
                  <i class="ni ni-ui-04"></i>
                  <span>ui-04</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
